import Button from "../components/atoms/Button";
import { styled } from "../utils/styled";
import { Pressable, ScrollView, Text, View } from "react-native";

export const Container = styled(ScrollView)`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
  padding: 24px;
`;

export const ContentContainer = styled(View)`
  flex: 1;
  align-items: center;
  gap: 24px;
`;

export const TopContainer = styled.View`
  flex: 1;
  align-items: center;
  justify-content: center;
  background-color: ${({ theme }) => theme.colors.background};
  padding: 24px;
`;
export const TopContentContainer = styled(View)`
  flex: 1;
  justify-content: center;
  width: 100%;
  align-items: center;
  gap: 24px;
`;

export const BottomContentContainer = styled(View)`
  flex: 1;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: ${({ theme }) => theme.colors.card};
  border-radius: 20px;
  margin-top: 100px;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
`;

export const Heading = styled(Text)`
  color: ${({ theme }) => theme.colors.statusEstimation};
  font-weight: bold;
  font-size: 32px;
  text-align: center;
`;

export const Question = styled(Text)`
  color: ${({ theme }) => theme.colors.text};
  font-size: 18px;
  text-align: center;
  font-weight: bold;
  line-height: 30px;
`;

export const LinkContainer = styled(View)`
  flex: 1;
  width: 100%;
  justify-content: flex-end;
`;

export const LinkButton = styled(Pressable)`
  width: 100%;
  align-items: center;
  justify-content: center;
  padding: 12px;
`;

export const LinkText = styled(Text)`
  color: ${({ theme }) => theme.colors.primary};
  font-weight: 600;
  font-size: 14px;
  text-align: center;
`;
export const ButtonContainer = styled(Button)`
  width: 100%;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.text};
`;

export const ModalButtonContainer = styled(View)`
  width: 100%;
  gap: 16px;
`;

export const RoleButton = styled(Pressable)<{ isSelected?: boolean }>`
  width: 100%;
  padding: 16px 24px;
  border-radius: 25px;
  border-width: 1px;
  border-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : theme.colors.divider};
  background-color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.primary : 'transparent'};
  align-items: center;
  justify-content: center;
`;

export const RoleButtonText = styled(Text)<{ isSelected?: boolean }>`
  color: ${({ theme, isSelected }) =>
    isSelected ? theme.colors.background : theme.colors.primary};
  font-size: 18px;
  font-weight: bold;
`;
